package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.form.entity.FormApprovalNode;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.mapper.WorkflowApprovalNodeMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowApprovalNodeRecordMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.constants.ConstantsWorkStudy;
import com.sanythadmin.project.workstudy.entity.*;
import com.sanythadmin.project.workstudy.enums.AdjustmentStatus;
import com.sanythadmin.project.workstudy.enums.InterviewResult;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import com.sanythadmin.project.workstudy.mapper.QgzxJobApplicationMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxJobTypeMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxStudentApplyMapper;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import com.sanythadmin.project.workstudy.service.QgzxInterviewRecordService;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationAddressService;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import com.sanythadmin.project.workstudy.service.QgzxStudentsClassTimeService;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getUserInfo;

/**
 * 学生岗位申请Service实现
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Slf4j
@Service
@AllArgsConstructor
public class QgzxStudentApplyServiceImpl extends ServiceImpl<QgzxStudentApplyMapper, QgzxStudentApply> implements QgzxStudentApplyService {

    private final UserInfoMapper userInfoMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;
    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final QgzxJobTypeMapper jobTypeMapper;
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final WorkflowApprovalNodeMapper approvalNodeMapper;
    private final WorkflowApprovalNodeRecordMapper approvalNodeRecordMapper;
    private final QgzxJobTypeMapper qgzxJobTypeMapper;
    private final QgzxEmployerService employerService;
    private final UserInfoService userInfoService;
    private final SysModuleSetupService sysModuleSetupService;
    private final WorkflowService workflowService;
    private final FormRestrictService formRestrictService;
    private final QgzxStudentsClassTimeService qgzxStudentsClassTimeService;
    private final QgzxInterviewRecordService qgzxInterviewRecordService;
    private final SysParamService sysParamService;
    private final QgzxJobApplicationAddressService qgzxJobApplicationAddressService;


    @Override
    public PageResult<QgzxJobApplicationVO> pageByApplicantList(QgzxJobApplicationParam param) {
        String username = SecurityUtil.getUsername();
        UserInfo userInfo = userInfoMapper.selectById(username);
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        param.setSpzt(ReviewResult.TongGuo);
        param.setXnxq(year);
        List<QgzxJobApplication> filteredList = filterApplicantJobs(param, username, userInfo, year);
        int total = filteredList.size();
        filteredList = CommonUtil.pageList(filteredList, param.getPage().intValue(), param.getLimit().intValue());
        List<QgzxJobApplicationVO> qgzxJobApplicationVOS = convertToVOListWithApplyCount(filteredList, year);
        return new PageResult<>(qgzxJobApplicationVOS, (long) total);
    }

    @Override
    public List<QgzxJobApplicationVO> listApplicantList(QgzxJobApplicationParam param) {
        String username = SecurityUtil.getUsername();
        UserInfo userInfo = userInfoMapper.selectById(username);
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        param.setSpzt(ReviewResult.TongGuo);
        param.setXnxq(year);
        List<QgzxJobApplication> filteredList = filterApplicantJobs(param, username, userInfo, year);
        return convertToVOListWithApplyCount(filteredList, year);
    }

    /**
     * 岗位过滤
     *
     * @param param 查询参数
     * @param username 当前用户名
     * @param userInfo 用户信息
     * @param year 学年学期
     * @return 过滤后的岗位列表（已排除已申请的岗位）
     */
    private List<QgzxJobApplication> filterApplicantJobs(QgzxJobApplicationParam param,String username,UserInfo userInfo,String year) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                        emp -> emp.association(UserInfo.class, QgzxEmployer::getUserInfo))
                .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .innerJoin(UserInfo.class, UserInfo::getXgh, QgzxEmployer::getXgh);
        LocalDate currentDate = LocalDate.now();
        wrapper.le(QgzxJobApplication::getStartDate, currentDate)
               .ge(QgzxJobApplication::getEndDate, currentDate);
        List<QgzxJobApplication> allJobApplications = jobApplicationMapper.selectList(wrapper);
        List<QgzxJobApplication> businessFilteredList = new ArrayList<>();
        for (QgzxJobApplication jobApplication : allJobApplications) {
            // 上课时间检查（只有当岗位开启了上课时间限制时才检查）
            if (JudgeMark.YES.equals(jobApplication.getSfxzsksj())) {
                String check = qgzxStudentsClassTimeService.check(userInfo, jobApplication);
                if (org.springframework.util.StringUtils.hasText(check)) continue;
            }
            // 检查岗位申请人数限制
            String error = checkLimit(username, jobApplication);
            if (org.springframework.util.StringUtils.hasText(error)) continue;
            // 检查限制条件
            error = formRestrictService.restrictCheck(jobApplication.getId(), userInfo, false);
            if (org.springframework.util.StringUtils.hasText(error)) continue;
            businessFilteredList.add(jobApplication);
        }
        List<QgzxJobApplication> filteredList = new ArrayList<>();
        if (!businessFilteredList.isEmpty()) {
            List<String> jobIds = businessFilteredList.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            List<String> appliedJobIdList = studentApplyMapper.selectAppliedJobIds(username, year, jobIds);
            Set<String> appliedJobIds = new HashSet<>(appliedJobIdList);
            for (QgzxJobApplication jobApplication : businessFilteredList) {
                if (!appliedJobIds.contains(jobApplication.getId())) {
                    filteredList.add(jobApplication);
                }
            }
        }

        return filteredList;
    }

    /**
     * 将岗位实体列表转换为VO列表，并批量设置申请人数
     *
     * @param jobList 岗位实体列表
     * @param year 学年学期
     * @return VO列表
     */
    private List<QgzxJobApplicationVO> convertToVOListWithApplyCount(List<QgzxJobApplication> jobList, String year) {
        List<QgzxJobApplicationVO> voList = new ArrayList<>();
        Map<String, Long> jobApplyCountMap = new HashMap<>();
        if (!jobList.isEmpty()) {
            List<String> jobIds = jobList.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            // 设置岗位地址信息
            Map<String, List<QgzxJobApplicationAddress>> addressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);
            for (QgzxJobApplication job : jobList) {
                job.setJobAddresses(addressMap.getOrDefault(job.getId(), new ArrayList<>()));
            }
            // 批量查询所有岗位的申请人数
            List<Map<String, Object>> applyCountResults = studentApplyMapper.selectApplyCountsByJobIds(jobIds, year);
            for (Map<String, Object> result : applyCountResults) {
                String jobId = (String) result.get("JOBID");
                Object countObj = result.get("APPLYCOUNT");
                jobApplyCountMap.put(jobId,  countObj != null?new BigDecimal(countObj.toString()).longValue() :0L);
            }
        }
        for (QgzxJobApplication jobApplication : jobList) {
            QgzxJobApplicationVO jobApplicationVO = new QgzxJobApplicationVO();
            BeanUtils.copyProperties(jobApplication, jobApplicationVO);
            Long applyCount = jobApplyCountMap.getOrDefault(jobApplication.getId(), 0L);
            jobApplicationVO.setApplyCount(applyCount);
            voList.add(jobApplicationVO);
        }
        return voList;
    }

    @Override
    public String checkApplyTime(QgzxJobApplication jobApplication) {
        return !jobApplication.applyTimeIsCorrect() ? "未在岗位时间范围内" : null;
    }

    @Override
    public String checkLimit(String xgh, QgzxJobApplication jobApplication) {
        //检查岗位申请人数限制
        String error = checkJobApplicationLimit(xgh, jobApplication);
        if (error != null) return error;
        //检查岗位类别在岗数量限制
        error = checkJobTypeWorkingLimit(xgh, jobApplication);
        if (error != null) return error;
        return null;
    }

    /**
     * 检查单个岗位的申请人数限制
     */
    private String checkJobApplicationLimit(String xgh, QgzxJobApplication jobApplication) {
        QueryWrapper<QgzxStudentApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(QgzxStudentApply::getXgh, xgh)
                .eq(QgzxStudentApply::getXnxq, jobApplication.getXnxq())
                .eq(QgzxStudentApply::getJobId, jobApplication.getId());

        if (!Objects.isNull(jobApplication.getYgrs())) {
            Long count = studentApplyMapper.selectCount(queryWrapper);
            if (count >= jobApplication.getYgrs()) {
                return "已达岗位最大申请人数";
            }
        }
        return null;
    }

    /**
     * 检查岗位类别的在岗数量限制
     * 基于岗位类别的ksqgws字段，限制学生在该类别下同时在岗的岗位数量
     */
    private String checkJobTypeWorkingLimit(String xgh, QgzxJobApplication jobApplication) {
        if (StringUtils.isEmpty(jobApplication.getJobTypeId())) {
            return null;
        }
        // 获取岗位类别信息
        QgzxJobType jobType = jobTypeMapper.selectById(jobApplication.getJobTypeId());
        if (jobType == null || jobType.getKyggws() == 0) {
            return null; // 0表示不限制
        }
        // 统计学生在该岗位类别下当前在岗的数量
        QueryWrapper<QgzxStudentApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(QgzxStudentApply::getXgh, xgh)
                .eq(QgzxStudentApply::getXnxq, jobApplication.getXnxq())
                .eq(QgzxStudentApply::getSpzt, ReviewResult.TongGuo)  // 已通过审批
                .eq(QgzxStudentApply::getYgzt, WorkStatus.YG)        // 正在用工
                .in(QgzxStudentApply::getJobId,
                    jobApplicationMapper.selectList(new LambdaQueryWrapper<QgzxJobApplication>()
                            .eq(QgzxJobApplication::getJobTypeId, jobApplication.getJobTypeId())
                            .eq(QgzxJobApplication::getXnxq, jobApplication.getXnxq()))
                            .stream().map(QgzxJobApplication::getId).collect(Collectors.toList()));

        Long currentWorkingCount = studentApplyMapper.selectCount(queryWrapper);
        if (currentWorkingCount >= jobType.getKyggws()) {
            return "您在该岗位类别下已达最大在岗数量限制(" + jobType.getKyggws() + "个)";
        }
        return null;
    }

    /**
     * 检查岗位录取人数限制
     * 如果录取人数(lqrs)为空，则按岗位数(ygrs)*2控制；如果不为空，则按录取人数控制
     */
    private void checkJobAdmissionLimit(QgzxJobApplication jobApplication, String jobId) {
        Integer maxAdmissionCount;
        if (jobApplication.getLqrs() != null && jobApplication.getLqrs() > 0) {
            maxAdmissionCount = jobApplication.getLqrs();
        } else if (jobApplication.getYgrs() != null && jobApplication.getYgrs() > 0) {
            maxAdmissionCount = jobApplication.getYgrs() * 2;
        } else {
            return;
        }
        //TODO 判断第一级节点通过就算通过,或者有被的刚好方案
        long approvedCount = studentApplyMapper.selectCount(
                new LambdaQueryWrapper<QgzxStudentApply>()
                        .eq(QgzxStudentApply::getJobId, jobId)
                        .eq(QgzxStudentApply::getXnxq, jobApplication.getXnxq())
                        .eq(QgzxStudentApply::getSpzt, ReviewResult.TongGuo)
        );

        AssertUtil.isTrue(approvedCount < maxAdmissionCount,
                String.format("审批失败：该岗位已达到最大录取人数限制（%d人）", maxAdmissionCount));
    }

    @Override
    public String checkIsEdit(String id) {
        return workflowService.checkApplicationInfoIsCanEdit(WorkflowApprovalNode.class, WorkflowApprovalNodeRecord.class, id);
    }


    @Transactional
    @Override
    public void editByApplicant(QgzxStudentApply studentApply) {
        UserInfo userInfo = getUserInfo();
        String xnxq = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(studentApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位不存在");
        AssertUtil.isTrue(Objects.equals(jobApplication.getSpzt(), ReviewResult.TongGuo), "岗位未审核通过");
        //检查上课时间（只有当岗位开启了上课时间限制时才检查）
        if (JudgeMark.YES.equals(jobApplication.getSfxzsksj())) {
            String error = qgzxStudentsClassTimeService.check(userInfo, jobApplication);
            AssertUtil.isTrue(StringUtils.isBlank(error), error);
        }
        // 检查申请时间
        String error = checkApplyTime(jobApplication);
        AssertUtil.isTrue(StringUtils.isBlank(error), error);
        // 检查岗位申请人数/用工人数限制
        error = checkLimit(userInfo.getXgh(), jobApplication);
        AssertUtil.isTrue(StringUtils.isBlank(error), error);
        // 检查限制条件
        error = formRestrictService.restrictCheck(jobApplication.getId(), userInfo, false);
        AssertUtil.isTrue(StringUtils.isBlank(error), error);
        // 检查是否已申请
        Long count = studentApplyMapper.selectCount(new LambdaQueryWrapper<QgzxStudentApply>()
                .eq(QgzxStudentApply::getXgh, userInfo.getXgh())
                .eq(QgzxStudentApply::getJobId, studentApply.getJobId())
                .eq(QgzxStudentApply::getXnxq, xnxq));
        AssertUtil.isTrue(count == 0, "已申请该岗位");
        studentApply.setXgh(userInfo.getXgh());
        // 设置申请时的角色ID
        studentApply.setRoleId(SecurityUtil.getRoleId());
        studentApply.setXnxq(xnxq);
        studentApply.setSqsj(LocalDateTime.now());
        studentApply.setSpzt(ReviewResult.DaiShenPi);
        studentApply.setSfts(jobApplication.getSfms());
        studentApply.setXxmc(jobApplication.getJobName());
        save(studentApply);

        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        String workflowId = workflowService.createApprovalNode(WorkflowApprovalNode.class, studentApply,
                new WorkflowParam(null, year, SysModule.qgzxSqgw.getCode()), userInfo);
        studentApply.setWorkflowId(workflowId);
        studentApply.setSpzt(ReviewResult.DaiShenPi);
        studentApplyMapper.updateById(studentApply);
    }

    @Override
    @Transactional
    public void delete(String... ids) {
        SysAccount account = SecurityUtil.getAccount();
        String approverId = CommonUtil.appendComma(account.getUsername(), account.getRole().getId());
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzxGwsb.getCode());
        for (String id : ids) {
            String workflowId = workflowService.checkApplicationInfoIsCanDelete(FormApprovalNode.class, FormApprovalNodeRecord.class,SysModule.qgzxGwsb.getCode(),id, year,approverId);
            if (workflowId != null) {
                QgzxStudentApply studentApply = getById(id);
                delete(studentApply, workflowId);
            }
        }
    }

    @Transactional
    @Override
    public void deleteByApplicant(String... ids) {
        String username = SecurityUtil.getUsername();
        for (String id : ids) {
            QgzxStudentApply studentApply = getById(id);
            AssertUtil.isTrue(studentApply != null, "申请记录不存在");
            AssertUtil.isTrue(Objects.equals(studentApply.getXgh(), username), "无权操作");
            AssertUtil.isTrue(Objects.equals(studentApply.getSpzt(), ReviewResult.DaiShenPi), "只能取消待审核的申请");
            String workflowId = checkIsEdit(id);
            delete(studentApply,workflowId);
        }
    }

    private void delete(QgzxStudentApply apply, String workflowId) {
        approvalNodeRecordMapper.delete(new LambdaQueryWrapper<WorkflowApprovalNodeRecord>()
                .eq(WorkflowApprovalNodeRecord::getApplicationId, apply.getId()).eq(WorkflowApprovalNodeRecord::getWorkflowId, workflowId));
        approvalNodeMapper.delete(new LambdaQueryWrapper<WorkflowApprovalNode>().eq(WorkflowApprovalNode::getApplicationId, apply.getId()).
                eq(WorkflowApprovalNode::getWorkflowId, workflowId));
        studentApplyMapper.deleteById(apply.getId());
    }

    @Override
    public PageResult<QgzxStudentApply> pageByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam) {
        String username = SecurityUtil.getUsername();

        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode()));
        }
        param.setXgh(username);

        // 使用多层级关联查询
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication,
                        job -> job.association("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                                emp -> emp.association(UserInfo.class, QgzxEmployer::getUserInfo)))
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .leftJoin(UserInfo.class, "emp_user", UserInfo::getXgh, QgzxEmployer::getXgh)
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);
        wrapper.orderByDesc(QgzxStudentApply::getSqsj);
        Page<QgzxStudentApply> page = wrapper.getPage();
        page = page(page, wrapper);

        List<QgzxStudentApply> records = page.getRecords();
        if (!records.isEmpty()) {
            List<String> jobIds = records.stream()
                    .filter(apply -> apply.getJobApplication() != null)
                    .map(apply -> apply.getJobApplication().getId())
                    .distinct()
                    .collect(Collectors.toList());

            if (!jobIds.isEmpty()) {
                Map<String, List<QgzxJobApplicationAddress>> jobAddressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);
                records.forEach(apply -> {
                    if (apply.getJobApplication() != null) {
                        String jobId = apply.getJobApplication().getId();
                        List<QgzxJobApplicationAddress> addresses = jobAddressMap.get(jobId);
                        if (addresses != null) {
                            apply.getJobApplication().setJobAddresses(addresses);
                        }
                    }
                });
            }
        }

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxStudentApply> listByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam) {
        String username = SecurityUtil.getUsername();

        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode()));
        }
        param.setXgh(username);

        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication,
                        job -> job.association("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                                emp -> emp.association(UserInfo.class, QgzxEmployer::getUserInfo)))
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .leftJoin(UserInfo.class, "emp_user", UserInfo::getXgh, QgzxEmployer::getXgh)
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh)
                .eq(QgzxStudentApply::getXgh, param.getXgh())
                .eq(QgzxStudentApply::getXnxq, param.getXnxq())
                .orderByDesc(QgzxStudentApply::getSqsj);
        wrapper.orderByDesc(QgzxStudentApply::getSqsj);
        List<QgzxStudentApply> list = list(wrapper);

        if (!list.isEmpty()) {
            List<String> jobIds = list.stream()
                    .filter(apply -> apply.getJobApplication() != null)
                    .map(apply -> apply.getJobApplication().getId())
                    .distinct()
                    .collect(Collectors.toList());

            if (!jobIds.isEmpty()) {
                Map<String, List<QgzxJobApplicationAddress>> jobAddressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);
                list.forEach(apply -> {
                    if (apply.getJobApplication() != null) {
                        String jobId = apply.getJobApplication().getId();
                        List<QgzxJobApplicationAddress> addresses = jobAddressMap.get(jobId);
                        if (addresses != null) {
                            apply.getJobApplication().setJobAddresses(addresses);
                        }
                    }
                });
            }
        }

        return list;
    }

    @Override
    public PageResult<QgzxStudentApply> pageByEmployer(QgzxStudentApplyParam param) {
        String username = SecurityUtil.getUsername();

        // 获取用人单位信息
        QgzxEmployer employer = employerService.getOne(new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, username));
        AssertUtil.isTrue(employer != null, "未找到用人单位信息");

        // 设置查询条件
        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode()));
        }

        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .eq("j.EID", employer.getId())
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);

        Page<QgzxStudentApply> page = wrapper.getPage();
        page = page(page, wrapper);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public QgzxStudentApply getDetail(String id) {
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication,
                        job -> job.association("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                                emp -> emp.association(UserInfo.class, QgzxEmployer::getUserInfo)))
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .leftJoin(UserInfo.class, "emp_user", UserInfo::getXgh, QgzxEmployer::getXgh)
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh)
                .eq(QgzxStudentApply::getId, id);

        QgzxStudentApply apply = getOne(wrapper);

        // 查询岗位地址信息
        if (apply != null && apply.getJobApplication() != null) {
            String jobId = apply.getJobApplication().getId();
            List<QgzxJobApplicationAddress> addresses = qgzxJobApplicationAddressService.getByJobId(jobId);
            apply.getJobApplication().setJobAddresses(addresses);
        }

        return apply;
    }

    @Override
    public PageResult<QgzxStudentApply> pageApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = approvalListQueryWrapper(param, userInfoParam, approvalNodeParam);
        Page<QgzxStudentApply> page = wrapper.getPage();
        page = page(page, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    public MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> approvalListQueryWrapper(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam) {
//        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper =
//                UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param, QgzxStudentApply::getXgh, userInfoParam, UserType.STUDENT);
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);
        wrapper.selectAll(QgzxStudentApply.class);

        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzxSqgw.getCode()));
        }
        wrapper.buildQueryCondition("u", userInfoParam);
        workflowService.getApprovalListSubQueryWrapper(wrapper, nodeApproverMapper, SysModule.qgzxSqgw.getCode(), param.getXnxq(), approvalNodeParam.getNodeId(), approvalNodeParam.getResult());
        return wrapper;
    }

    @Override
    public List<QgzxStudentApply> listApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = approvalListQueryWrapper(param, userInfoParam, approvalNodeParam);
        return list(wrapper);
    }
    @Transactional
    @Override
    public CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxStudentApply studentApply, Executor executor) {
        final QgzxStudentApply info = studentApply == null ? studentApplyMapper.selectById(record.getApplicationId()) : studentApply;
        return CompletableFuture.supplyAsync(() -> {
            // 检查是否正在调剂中，如果是则不允许审核
            if (isInAdjustmentProcess(info)) {
                throw new RuntimeException("该申请正在调剂流程中，无法进行审核操作");
            }

            UserInfo userInfo = userInfoMapper.get(info.getXgh());
            QgzxJobApplication qgzxJobApplication = jobApplicationMapper.selectById(info.getJobId());
            QgzxJobType qgzxJobType = qgzxJobTypeMapper.selectById(qgzxJobApplication.getJobTypeId());

            if (qgzxJobType != null && qgzxJobType.getKyggws() > 0 && ReviewResult.TongGuo.getText().equals(record.getResult())) {
                List<String> jobIdsOfSameType = jobApplicationMapper.selectList(new LambdaQueryWrapper<QgzxJobApplication>()
                        .eq(QgzxJobApplication::getJobTypeId, qgzxJobType.getId())
                        .select(QgzxJobApplication::getId)).stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
                long appliedCount = 0;
                if (!jobIdsOfSameType.isEmpty()) {
                    appliedCount = studentApplyMapper.selectCount(
                            new LambdaQueryWrapper<QgzxStudentApply>()
                                    .eq(QgzxStudentApply::getXgh, info.getXgh())
                                    .in(QgzxStudentApply::getJobId, jobIdsOfSameType)
                                    .eq(QgzxStudentApply::getYgzt, WorkStatus.YG)
//                                .ne(QgzxStudentApply::getSpzt, ReviewResult.TuiHui.getText())
                    );
                }
                AssertUtil.isTrue(appliedCount < qgzxJobType.getKyggws(),
                        "审批失败：学生申请【" + qgzxJobType.getName() + "】类别的岗位已达到上限（" + qgzxJobType.getKyggws() + "个）。");
            }

            // 检查岗位录取人数限制
            if (ReviewResult.TongGuo.getText().equals(record.getResult())) {
                checkJobAdmissionLimit(qgzxJobApplication, info.getJobId());
            }

            ApprovalInfo<WorkflowApprovalNode, WorkflowApprovalNodeRecord> approvalInfo = workflowService.approve(WorkflowApprovalNode.class, record, info, new WorkflowParam(info.getWorkflowId()), userInfo);
            if(JudgeMark.YES.getText().equals(approvalInfo.getCurrentApprovalNode().getEndNode().getText()) &&
                    ReviewResult.TongGuo.getText().equals(approvalInfo.getReviewResult().getText())){
                boolean canSetWorkStatus = true;
                if (JudgeMark.YES.equals(qgzxJobApplication.getSfms())) {
                    QgzxInterviewRecord interviewRecord = qgzxInterviewRecordService.getByStudentApplyId(info.getId());
                    if (interviewRecord == null) {
                        // 自动创建待面试记录
                        createInterviewRecord(info.getId());
                        canSetWorkStatus = false; // 需要等待面试通过
                    } else if (!InterviewResult.TongGuo.equals(interviewRecord.getInterviewResult())) {
                        // 面试结果不是通过，不能设置用工状态
                        canSetWorkStatus = false;
                    }
                }

                if (canSetWorkStatus) {
                    // 审核通过是否直接用工
                    String autoSetWorkStatus = sysParamService.getParamValue(ConstantsWorkStudy.SHENHE_TONGGUO_ZHIJIE_YONGGONG);
                    WorkStatus defaultStatus = JudgeMark.YES.getText().equals(autoSetWorkStatus) ?
                            WorkStatus.YG : WorkStatus.DSG;

                    studentApplyMapper.update(new LambdaUpdateWrapper<QgzxStudentApply>()
                            .set(QgzxStudentApply::getSpzt, approvalInfo.getReviewResult())
                            .set(QgzxStudentApply::getYgzt, defaultStatus)
                            .eq(QgzxStudentApply::getId, info.getId()));
                } else {
                    // 只更新审批状态，不设置用工状态
                    studentApplyMapper.update(new LambdaUpdateWrapper<QgzxStudentApply>()
                            .set(QgzxStudentApply::getSpzt, approvalInfo.getReviewResult())
                            .eq(QgzxStudentApply::getId, info.getId()));
                }
            } else {
                studentApplyMapper.update(new LambdaUpdateWrapper<QgzxStudentApply>()
                        .set(QgzxStudentApply::getSpzt, approvalInfo.getReviewResult())
                        .eq(QgzxStudentApply::getId, info.getId()));
            }
            return approvalInfo.getNextApprovableNodeId();
        }, executor);



    }

    @Override
    public PageResult<QgzxStudentApply> pageForInterview(QgzxStudentApplyParam param) {
        String username = SecurityUtil.getUsername();
        QgzxEmployer employer = employerService.getOne(new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, username));
        AssertUtil.isTrue(employer != null, "未找到用人单位信息");

        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode()));
        }

        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxStudentApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .eq("j.EID", employer.getId())
                .eq("j.SFMS", JudgeMark.YES) // 只查询需要面试的岗位
                .eq(QgzxStudentApply::getSpzt, ReviewResult.TongGuo) // 只查询审核通过的申请
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);

        // 排除已有面试记录的申请
        wrapper.notExists("SELECT 1 FROM SYT_QGZX_INTERVIEW_RECORD ir WHERE ir.STUDENT_APPLY_ID = " + wrapper.getAlias() + ".ID");

        Page<QgzxStudentApply> page = wrapper.getPage();
        page = page(page, wrapper);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 自动创建待面试记录
     *
     * @param studentApplyId 学生申请ID
     */
    private void createInterviewRecord(String studentApplyId) {
        try {
            QgzxInterviewRecord interviewRecord = new QgzxInterviewRecord();
            interviewRecord.setStudentApplyId(studentApplyId);
            interviewRecord.setInterviewResult(InterviewResult.DaiMianShi);
            interviewRecord.setCreateTime(LocalDateTime.now());
            interviewRecord.setCreateBy(SecurityUtil.getUsername());

            qgzxInterviewRecordService.save(interviewRecord);

            log.info("自动创建待面试记录成功: studentApplyId={}", studentApplyId);
        } catch (Exception e) {
            log.error("自动创建待面试记录失败: studentApplyId={}, error={}", studentApplyId, e.getMessage(), e);
        }
    }


    @Transactional
    @Override
    public void applyAdjustment(String studentApplyId, String targetJobCode, String reason) {
        //根据岗位编码或id查询岗位
        QgzxJobApplication targetJob = jobApplicationMapper.selectOne(new LambdaQueryWrapper<QgzxJobApplication>()
                .eq(QgzxJobApplication::getJobCode, targetJobCode)
                .or()
                .eq(QgzxJobApplication::getId, targetJobCode));

        String targetJobId = targetJob.getId();
        String validationError = validateAdjustment(studentApplyId, targetJobId);
        AssertUtil.isTrue(StringUtils.isBlank(validationError), validationError);
        QgzxStudentApply originalApply = getById(studentApplyId);
        UserInfo userInfo = userInfoService.get(originalApply.getXgh());

        originalApply.setTjzt(AdjustmentStatus.TiaoJiWanCheng);
        originalApply.setTjsqsj(LocalDateTime.now());
        originalApply.setSpzt(ReviewResult.BuTongGuo); // 设置为审核不通过
        originalApply.setOriginalJobId(originalApply.getJobId()); // 保存原岗位ID
        if (StringUtils.isNotEmpty(originalApply.getWorkflowId())) {
            try {
                 workflowService.terminateWorkflow(originalApply.getWorkflowId(), "学生申请已调剂到其他岗位");
                log.info("终止原有工作流: workflowId={}", originalApply.getWorkflowId());
            } catch (Exception e) {
                log.error("终止工作流失败: workflowId={}, error={}", originalApply.getWorkflowId(), e.getMessage(), e);
            }
        }
        updateById(originalApply);

        QgzxStudentApply newApply = new QgzxStudentApply();
        newApply.setXgh(originalApply.getXgh());
        newApply.setJobId(targetJobId);
        newApply.setXnxq(originalApply.getXnxq());
        newApply.setSqsj(LocalDateTime.now());
        newApply.setSqly(reason);
        newApply.setSpzt(ReviewResult.DaiShenPi);
        newApply.setYgzt(WorkStatus.DSG);
        newApply.setTjzt(AdjustmentStatus.WuTiaoJi);
        newApply.setXxmc(targetJob.getJobName());
        newApply.setSfts(originalApply.getSfts());
        newApply.setSffcap(originalApply.getSffcap());
        newApply.setTcys(originalApply.getTcys());
        save(newApply);

        try {
            String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
            String workflowId = workflowService.createApprovalNode(WorkflowApprovalNode.class, newApply,
                    new WorkflowParam(null, year, SysModule.qgzxSqgw.getCode()), userInfo);
            newApply.setWorkflowId(workflowId);
            updateById(newApply);

            log.info("调剂完成，创建新申请: originalApplyId={}, newApplyId={}, targetJobId={}, workflowId={}",
                    studentApplyId, newApply.getId(), targetJobId, workflowId);
        } catch (Exception e) {
            log.error("为新申请创建工作流失败: newApplyId={}, error={}", newApply.getId(), e.getMessage(), e);
            throw new RuntimeException("调剂失败：无法创建审核流程");
        }
        createAdjustmentRecord(originalApply, newApply, targetJobId, reason);
        log.info("用人单位申请调剂成功: originalApplyId={}, newApplyId={}, targetJobId={}, reason={}",
                studentApplyId, newApply.getId(), targetJobId, reason);
    }

    @Override
    public List<QgzxStudentApply> getAdjustmentHistory(String studentApplyId) {
        QgzxStudentApply studentApply = getById(studentApplyId);
        AssertUtil.isTrue(studentApply != null, "学生申请不存在");

        // 查询该学生在同一学年学期的所有调剂相关申请记录
        LambdaQueryWrapper<QgzxStudentApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QgzxStudentApply::getXgh, studentApply.getXgh())
                .eq(QgzxStudentApply::getXnxq, studentApply.getXnxq())
                .eq(QgzxStudentApply::getTjzt, AdjustmentStatus.TiaoJiWanCheng)
                .orderByDesc(QgzxStudentApply::getTjsqsj);

        List<QgzxStudentApply> adjustedApplies = list(wrapper);

        // 如果当前申请本身就是调剂完成的，也包含在结果中
        if (AdjustmentStatus.TiaoJiWanCheng.equals(studentApply.getTjzt()) &&
            adjustedApplies.stream().noneMatch(apply -> apply.getId().equals(studentApplyId))) {
            adjustedApplies.add(0, studentApply);
        }

        return adjustedApplies;
    }

    @Override
    public String validateAdjustment(String studentApplyId, String targetJobId) {
        QgzxStudentApply studentApply = getById(studentApplyId);
        if (studentApply == null) {
            return "学生申请不存在";
        }
        if (!ReviewResult.DaiShenPi.equals(studentApply.getSpzt()) &&
            !ReviewResult.ShenPiZhong.equals(studentApply.getSpzt())) {
            return "只能调剂待审批或审批中的申请";
        }
        if (studentApply.getTjzt() != null &&
            AdjustmentStatus.TiaoJiWanCheng.equals(studentApply.getTjzt())) {
            return "该申请已调剂完成，无法重复调剂";
        }
        QgzxJobApplication targetJob = jobApplicationMapper.selectById(targetJobId);
        if (targetJob == null) {
            return "目标岗位不存在";
        }
        if (!ReviewResult.TongGuo.equals(targetJob.getSpzt())) {
            return "目标岗位未审核通过";
        }
        if (!studentApply.getXnxq().equals(targetJob.getXnxq())) {
            return "目标岗位与当前申请不在同一学年学期";
        }
        if (studentApply.getJobId().equals(targetJobId)) {
            return "不能调剂到相同的岗位";
        }
        Long currentApplyCount = studentApplyMapper.countByJobIdAndXnxq(targetJobId, targetJob.getXnxq());
        if (currentApplyCount >= targetJob.getYgrs()) {
            return "目标岗位已满员，无法调剂";
        }
        String username = SecurityUtil.getUsername();
        String roleScope = SecurityUtil.getRoleScope();
        if (Constants.ROLE_SCOPE_QX.equals(roleScope)) {
            return null;
        }
        QgzxJobApplication currentJob = jobApplicationMapper.selectById(studentApply.getJobId());
        if (currentJob != null && StringUtils.isNotEmpty(currentJob.getEid())) {
            QgzxEmployer employer = employerService.getById(currentJob.getEid());
            if (employer == null || !username.equals(employer.getXgh())) {
                return "无权限进行调剂操作";
            }
        }
        return null;
    }

    /**
     * 创建调剂记录
     */
    private void createAdjustmentRecord(QgzxStudentApply originalApply, QgzxStudentApply newApply, String targetJobId, String reason) {
        try {
            QgzxAdjustmentRecord record = new QgzxAdjustmentRecord();
            record.setOriginalStudentApplyId(originalApply.getId());
            record.setNewStudentApplyId(newApply.getId());
            record.setFromJobId(originalApply.getOriginalJobId());
            record.setToJobId(targetJobId);
            record.setStatus(AdjustmentStatus.TiaoJiWanCheng);
            record.setApplicantXgh(SecurityUtil.getUsername());
            record.setReason(reason);
            record.setApplyTime(LocalDateTime.now());
            record.setCompleteTime(LocalDateTime.now()); // 简化版：调剂立即完成
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());

            // 这里需要注入QgzxAdjustmentRecordMapper
            // adjustmentRecordMapper.insert(record);
            log.info("创建调剂记录: originalApplyId={}, newApplyId={}, fromJobId={}, toJobId={}",
                    originalApply.getId(), newApply.getId(), originalApply.getOriginalJobId(), targetJobId);
        } catch (Exception e) {
            log.error("创建调剂记录失败: originalApplyId={}, error={}", originalApply.getId(), e.getMessage(), e);
        }
    }

    /**
     * 检查申请是否正在调剂中或工作流已终止（简化版：调剂完成的申请不允许审核）
     */
    private boolean isInAdjustmentProcess(QgzxStudentApply studentApply) {
        if (studentApply.getTjzt() == null) {
            return false;
        }
        // 简化版：只有调剂完成的申请不允许审核
        return AdjustmentStatus.TiaoJiWanCheng.equals(studentApply.getTjzt());
    }

}